import React from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Truck, Package, MapPin, Star, Sparkles, Zap, Crown,
  Heart, Gift, Bot, Shield, Gem, TrendingUp, Flame
} from 'lucide-react';
import Logo from '../common/Logo';

const AuthLayout: React.FC = () => {
  const floatingElements = [
    { icon: Truck, delay: 0, x: 15, y: 20, color: 'text-blue-400' },
    { icon: Package, delay: 0.5, x: 85, y: 15, color: 'text-orange-400' },
    { icon: MapPin, delay: 1, x: 10, y: 80, color: 'text-green-400' },
    { icon: Star, delay: 1.5, x: 90, y: 85, color: 'text-yellow-400' },
    { icon: Bot, delay: 2, x: 20, y: 60, color: 'text-purple-400' },
    { icon: Gift, delay: 2.5, x: 80, y: 50, color: 'text-pink-400' },
    { icon: Crown, delay: 3, x: 50, y: 10, color: 'text-primary-400' },
    { icon: Gem, delay: 3.5, x: 5, y: 40, color: 'text-cyan-400' },
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Spectacular Multi-Dimensional Background */}
      <div className="fixed inset-0">
        {/* Base Dynamic Gradient */}
        <motion.div
          animate={{
            background: [
              'linear-gradient(135deg, #0f172a 0%, #1e1b4b 25%, #581c87 50%, #7c2d12 75%, #0f172a 100%)',
              'linear-gradient(225deg, #1e1b4b 0%, #581c87 25%, #7c2d12 50%, #0f172a 75%, #1e1b4b 100%)',
              'linear-gradient(315deg, #581c87 0%, #7c2d12 25%, #0f172a 50%, #1e1b4b 75%, #581c87 100%)',
              'linear-gradient(45deg, #7c2d12 0%, #0f172a 25%, #1e1b4b 50%, #581c87 75%, #7c2d12 100%)',
              'linear-gradient(135deg, #0f172a 0%, #1e1b4b 25%, #581c87 50%, #7c2d12 75%, #0f172a 100%)'
            ]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute inset-0"
        />

        {/* Animated Mesh Overlay */}
        <motion.div
          animate={{
            background: [
              'radial-gradient(circle at 20% 30%, rgba(117, 41, 179, 0.4) 0%, transparent 50%), radial-gradient(circle at 80% 70%, rgba(103, 179, 41, 0.3) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(143, 61, 210, 0.35) 0%, transparent 50%)',
              'radial-gradient(circle at 70% 20%, rgba(117, 41, 179, 0.35) 0%, transparent 50%), radial-gradient(circle at 30% 80%, rgba(103, 179, 41, 0.4) 0%, transparent 50%), radial-gradient(circle at 80% 40%, rgba(143, 61, 210, 0.3) 0%, transparent 50%)',
              'radial-gradient(circle at 50% 50%, rgba(117, 41, 179, 0.3) 0%, transparent 50%), radial-gradient(circle at 10% 30%, rgba(103, 179, 41, 0.35) 0%, transparent 50%), radial-gradient(circle at 90% 70%, rgba(143, 61, 210, 0.4) 0%, transparent 50%)',
              'radial-gradient(circle at 30% 70%, rgba(117, 41, 179, 0.4) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(103, 179, 41, 0.3) 0%, transparent 50%), radial-gradient(circle at 20% 20%, rgba(143, 61, 210, 0.35) 0%, transparent 50%)'
            ]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute inset-0"
        />

        {/* Massive Floating Orbs */}
        <motion.div
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.15, 0.4, 0.15],
            rotate: [0, 180, 360],
            x: [-100, 100, -100],
            y: [-50, 50, -50],
          }}
          transition={{
            duration: 35,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-60 -left-60 w-[500px] h-[500px] bg-gradient-to-br from-primary-500/40 to-secondary-500/40 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.3, 1, 1.3],
            opacity: [0.2, 0.5, 0.2],
            rotate: [360, 180, 0],
            x: [80, -80, 80],
            y: [40, -40, 40],
          }}
          transition={{
            duration: 40,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 8
          }}
          className="absolute -bottom-60 -right-60 w-[450px] h-[450px] bg-gradient-to-br from-third-500/40 to-primary-500/40 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.6, 1],
            opacity: [0.1, 0.35, 0.1],
            rotate: [0, -180, -360],
            x: [60, -60, 60],
            y: [-30, 30, -30],
          }}
          transition={{
            duration: 45,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 15
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] bg-gradient-to-br from-secondary-500/30 to-fourth-500/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.25, 0.45, 0.25],
            rotate: [180, 0, 180],
            x: [-40, 40, -40],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 20
          }}
          className="absolute top-1/4 right-1/4 w-[350px] h-[350px] bg-gradient-to-br from-primary-500/35 to-third-500/35 rounded-full blur-3xl"
        />

        {/* Enhanced Floating Particles System */}
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${2 + Math.random() * 4}px`,
              height: `${2 + Math.random() * 4}px`,
              background: i % 4 === 0 ? '#7529B3' :
                         i % 4 === 1 ? '#67B329' :
                         i % 4 === 2 ? '#8F3DD2' :
                         '#E9E1F0',
              boxShadow: `0 0 ${8 + Math.random() * 15}px currentColor`,
            }}
            animate={{
              y: [-30, -120, -30],
              x: [0, Math.random() * 40 - 20, 0],
              opacity: [0, 1, 0],
              scale: [0.3, 1.5, 0.3],
              rotate: [0, 360],
            }}
            transition={{
              duration: 5 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Cosmic Dust Effect */}
        {[...Array(80)].map((_, i) => (
          <motion.div
            key={`dust-${i}`}
            className="absolute w-px h-px bg-white/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-10, -40, -10],
              opacity: [0, 0.8, 0],
              scale: [0.5, 2, 0.5],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "linear"
            }}
          />
        ))}

        {/* Shooting Stars */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`star-${i}`}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: '2px',
              height: `${20 + Math.random() * 30}px`,
              background: 'linear-gradient(to bottom, transparent, #ffffff, transparent)',
              transform: `rotate(${-45 + Math.random() * 90}deg)`
            }}
            animate={{
              opacity: [0, 1, 0],
              scaleY: [0, 1, 0],
              x: [0, 100],
              y: [0, 50],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatDelay: 8 + Math.random() * 10,
              delay: Math.random() * 5,
              ease: "easeOut"
            }}
          />
        ))}

        {/* Energy Waves */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`wave-${i}`}
            className="absolute border border-primary-400/20 rounded-full"
            style={{
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              width: `${200 + i * 100}px`,
              height: `${200 + i * 100}px`,
            }}
            animate={{
              scale: [0, 2],
              opacity: [0.5, 0],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              delay: i * 0.8,
              ease: "easeOut"
            }}
          />
        ))}

        {/* Spectacular Floating Service Icons */}
        {floatingElements.map((element, index) => (
          <motion.div
            key={`service-icon-${index}`}
            className="absolute"
            style={{ left: `${element.x}%`, top: `${element.y}%` }}
            animate={{
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              rotate: [-15, 15, -15],
              scale: [0.7, 1.3, 0.7],
            }}
            transition={{
              duration: 6 + index * 0.8,
              repeat: Infinity,
              delay: element.delay,
              ease: "easeInOut"
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.15, 0.4, 0.15],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: element.delay * 0.7,
              }}
              className="relative"
            >
              {/* Icon with enhanced glow */}
              <motion.div
                animate={{
                  filter: [
                    'brightness(1) saturate(1)',
                    'brightness(1.5) saturate(1.8)',
                    'brightness(1) saturate(1)'
                  ]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: element.delay
                }}
              >
                <element.icon size={48} className={`${element.color} drop-shadow-2xl`} />
              </motion.div>

              {/* Multi-layer glow effect */}
              <motion.div
                className="absolute inset-0 rounded-full blur-2xl"
                style={{
                  background: `radial-gradient(circle, ${
                    element.color.includes('blue') ? '#3B82F6' :
                    element.color.includes('orange') ? '#F97316' :
                    element.color.includes('green') ? '#10B981' :
                    element.color.includes('yellow') ? '#F59E0B' :
                    element.color.includes('purple') ? '#8B5CF6' :
                    element.color.includes('pink') ? '#EC4899' :
                    element.color.includes('primary') ? '#7529B3' :
                    '#06B6D4'
                  }40 0%, transparent 70%)`
                }}
                animate={{
                  scale: [1, 2, 1],
                  opacity: [0.3, 0.6, 0.3],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: element.delay,
                }}
              />

              {/* Outer glow ring */}
              <motion.div
                className="absolute inset-0 rounded-full blur-3xl"
                style={{
                  background: `radial-gradient(circle, ${
                    element.color.includes('blue') ? '#3B82F6' :
                    element.color.includes('orange') ? '#F97316' :
                    element.color.includes('green') ? '#10B981' :
                    element.color.includes('yellow') ? '#F59E0B' :
                    element.color.includes('purple') ? '#8B5CF6' :
                    element.color.includes('pink') ? '#EC4899' :
                    element.color.includes('primary') ? '#7529B3' :
                    '#06B6D4'
                  }20 0%, transparent 80%)`
                }}
                animate={{
                  scale: [1.5, 3, 1.5],
                  opacity: [0.1, 0.3, 0.1],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  delay: element.delay + 1,
                }}
              />
            </motion.div>
          </motion.div>
        ))}

        {/* Additional Geometric Elements */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={`geo-${i}`}
            className="absolute"
            style={{
              left: `${10 + (i * 8)}%`,
              top: `${15 + (i * 6)}%`,
              width: `${20 + Math.random() * 30}px`,
              height: `${20 + Math.random() * 30}px`,
              background: `linear-gradient(${Math.random() * 360}deg,
                rgba(117, 41, 179, ${0.1 + Math.random() * 0.2}),
                rgba(103, 179, 41, ${0.1 + Math.random() * 0.2}))`,
              borderRadius: i % 3 === 0 ? '50%' : i % 3 === 1 ? '0%' : '25%',
              filter: 'blur(1px)'
            }}
            animate={{
              rotate: [0, 360],
              scale: [0.5, 1.2, 0.5],
              opacity: [0.1, 0.4, 0.1],
              x: [-15, 15, -15],
              y: [-10, 10, -10],
            }}
            transition={{
              duration: 12 + Math.random() * 8,
              repeat: Infinity,
              delay: Math.random() * 4,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Enhanced Lightning Storm */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={`lightning-${i}`}
            className="absolute"
            style={{
              left: `${10 + (i * 8)}%`,
              top: `${5 + (i * 8)}%`,
              width: '3px',
              height: `${25 + Math.random() * 35}px`,
              background: `linear-gradient(to bottom,
                transparent,
                ${i % 3 === 0 ? '#7529B3' : i % 3 === 1 ? '#67B329' : '#8F3DD2'},
                transparent)`,
              transform: `rotate(${-45 + Math.random() * 90}deg)`,
              filter: 'brightness(1.5) saturate(1.8)',
            }}
            animate={{
              opacity: [0, 1, 0],
              scaleY: [0, 1, 0],
              filter: [
                'brightness(1) saturate(1)',
                'brightness(3) saturate(2)',
                'brightness(1) saturate(1)'
              ]
            }}
            transition={{
              duration: 0.2 + Math.random() * 0.3,
              repeat: Infinity,
              repeatDelay: 6 + Math.random() * 8,
              delay: Math.random() * 4,
            }}
          />
        ))}

        {/* Plasma Bolts */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`plasma-${i}`}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: '4px',
              height: '4px',
              background: '#7529B3',
              borderRadius: '50%',
              boxShadow: '0 0 20px #7529B3, 0 0 40px #7529B3, 0 0 60px #7529B3',
            }}
            animate={{
              scale: [0, 2, 0],
              opacity: [0, 1, 0],
              boxShadow: [
                '0 0 0px #7529B3',
                '0 0 30px #7529B3, 0 0 60px #7529B3, 0 0 90px #7529B3',
                '0 0 0px #7529B3'
              ]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              repeatDelay: 5 + Math.random() * 5,
              delay: Math.random() * 3,
            }}
          />
        ))}

        {/* Nebula Clouds */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`nebula-${i}`}
            className="absolute rounded-full blur-3xl"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${150 + Math.random() * 200}px`,
              height: `${100 + Math.random() * 150}px`,
              background: `radial-gradient(ellipse,
                rgba(117, 41, 179, ${0.1 + Math.random() * 0.15}) 0%,
                rgba(103, 179, 41, ${0.05 + Math.random() * 0.1}) 50%,
                transparent 100%)`,
            }}
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.3, 0.6, 0.3],
              rotate: [0, 180, 360],
              x: [-30, 30, -30],
              y: [-20, 20, -20],
            }}
            transition={{
              duration: 25 + Math.random() * 15,
              repeat: Infinity,
              delay: Math.random() * 10,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Cosmic Grid */}
        <motion.div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(rgba(117, 41, 179, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(117, 41, 179, 0.3) 1px, transparent 1px),
              linear-gradient(rgba(103, 179, 41, 0.2) 1px, transparent 1px),
              linear-gradient(90deg, rgba(103, 179, 41, 0.2) 1px, transparent 1px)
            `,
            backgroundSize: '100px 100px, 100px 100px, 50px 50px, 50px 50px'
          }}
          animate={{
            backgroundPosition: [
              '0px 0px, 0px 0px, 0px 0px, 0px 0px',
              '100px 100px, 100px 100px, 50px 50px, 50px 50px'
            ]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="w-full max-w-lg"
        >
          {/* Enhanced Glass Container */}
          <motion.div
            className="relative"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            {/* Glow Effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-3xl blur-xl"
              animate={{
                scale: [1, 1.05, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            <div className="relative bg-white/90 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/30 p-8 overflow-hidden">
              {/* Subtle Pattern Overlay */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                  backgroundImage: `radial-gradient(circle at 25% 25%, #7529B3 2px, transparent 2px),
                                   radial-gradient(circle at 75% 75%, #67B329 2px, transparent 2px)`,
                  backgroundSize: '50px 50px'
                }} />
              </div>

              {/* Logo Section */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center mb-8 relative z-10"
              >
                <div className="flex justify-center mb-4">
                  <motion.div
                    animate={{
                      scale: [1, 1.02, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Logo size="lg" />
                  </motion.div>
                </div>
              </motion.div>

              {/* Content */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="relative z-10"
              >
                <Outlet />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default AuthLayout;
