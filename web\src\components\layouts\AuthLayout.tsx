import React from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Truck, Package, MapPin, Star, Sparkles, Zap, Crown,
  Heart, Gift, Bot, Shield, Gem, TrendingUp, Flame
} from 'lucide-react';
import Logo from '../common/Logo';

const AuthLayout: React.FC = () => {
  const floatingElements = [
    { icon: Truck, delay: 0, x: 15, y: 20, color: 'text-blue-400' },
    { icon: Package, delay: 0.5, x: 85, y: 15, color: 'text-orange-400' },
    { icon: MapPin, delay: 1, x: 10, y: 80, color: 'text-green-400' },
    { icon: Star, delay: 1.5, x: 90, y: 85, color: 'text-yellow-400' },
    { icon: Bot, delay: 2, x: 20, y: 60, color: 'text-purple-400' },
    { icon: Gift, delay: 2.5, x: 80, y: 50, color: 'text-pink-400' },
    { icon: Crown, delay: 3, x: 50, y: 10, color: 'text-primary-400' },
    { icon: Gem, delay: 3.5, x: 5, y: 40, color: 'text-cyan-400' },
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Large Animated Gradient Orbs */}
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.4, 0.2],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-40 -left-40 w-96 h-96 bg-gradient-to-br from-primary-500/30 to-secondary-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.3, 0.6, 0.3],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -bottom-40 -right-40 w-80 h-80 bg-gradient-to-br from-third-500/30 to-primary-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.1, 0.3, 0.1],
            rotate: [0, -180, -360],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 10
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-br from-secondary-500/20 to-third-600/20 rounded-full blur-3xl"
        />

        {/* Floating Particles */}
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -100, -20],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}

        {/* Enhanced Floating Icons */}
        {floatingElements.map((element, index) => (
          <motion.div
            key={index}
            className="absolute opacity-10"
            style={{ left: `${element.x}%`, top: `${element.y}%` }}
            animate={{
              y: [-15, 15, -15],
              rotate: [-10, 10, -10],
              scale: [0.8, 1.2, 0.8],
            }}
            transition={{
              duration: 4 + index * 0.5,
              repeat: Infinity,
              delay: element.delay,
              ease: "easeInOut"
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: element.delay * 0.5,
              }}
              className="relative"
            >
              <element.icon size={40} className={`${element.color} drop-shadow-lg`} />
              <motion.div
                className="absolute inset-0 rounded-full blur-xl"
                style={{
                  background: `radial-gradient(circle, ${element.color.includes('blue') ? '#3B82F6' :
                    element.color.includes('orange') ? '#F97316' :
                    element.color.includes('green') ? '#10B981' :
                    element.color.includes('yellow') ? '#F59E0B' :
                    element.color.includes('purple') ? '#8B5CF6' :
                    element.color.includes('pink') ? '#EC4899' :
                    element.color.includes('primary') ? '#7529B3' :
                    '#06B6D4'}20 0%, transparent 70%)`
                }}
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.2, 0.4, 0.2],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: element.delay,
                }}
              />
            </motion.div>
          </motion.div>
        ))}

        {/* Lightning Effects */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-2 h-20 bg-gradient-to-b from-yellow-400 to-transparent"
          animate={{
            opacity: [0, 1, 0],
            scaleY: [0, 1, 0],
          }}
          transition={{
            duration: 0.3,
            repeat: Infinity,
            repeatDelay: 8,
          }}
          style={{
            clipPath: 'polygon(0% 0%, 100% 0%, 80% 50%, 100% 100%, 0% 100%, 20% 50%)',
          }}
        />
        <motion.div
          className="absolute top-3/4 right-1/3 w-2 h-16 bg-gradient-to-b from-blue-400 to-transparent"
          animate={{
            opacity: [0, 1, 0],
            scaleY: [0, 1, 0],
          }}
          transition={{
            duration: 0.2,
            repeat: Infinity,
            repeatDelay: 12,
            delay: 4,
          }}
          style={{
            clipPath: 'polygon(0% 0%, 100% 0%, 80% 50%, 100% 100%, 0% 100%, 20% 50%)',
          }}
        />
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="w-full max-w-lg"
        >
          {/* Enhanced Glass Container */}
          <motion.div
            className="relative"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            {/* Glow Effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-3xl blur-xl"
              animate={{
                scale: [1, 1.05, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            <div className="relative bg-white/90 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/30 p-8 overflow-hidden">
              {/* Subtle Pattern Overlay */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                  backgroundImage: `radial-gradient(circle at 25% 25%, #7529B3 2px, transparent 2px),
                                   radial-gradient(circle at 75% 75%, #67B329 2px, transparent 2px)`,
                  backgroundSize: '50px 50px'
                }} />
              </div>

              {/* Logo Section */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center mb-8 relative z-10"
              >
                <div className="flex justify-center mb-4">
                  <motion.div
                    animate={{
                      scale: [1, 1.02, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Logo size="lg" />
                  </motion.div>
                </div>
              </motion.div>

              {/* Content */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="relative z-10"
              >
                <Outlet />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default AuthLayout;
